package com.gusto.match.model.entity.photo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 相册折扣配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
public class PhotoAlbumDiscount implements Serializable {

    private static final long serialVersionUID = 6608166015576999709L;

    @Schema(description = "购买数量")
    private Integer quantity;

    @Schema(description = "折扣比例，0.3表示30%")
    private BigDecimal discountRatio;

}
