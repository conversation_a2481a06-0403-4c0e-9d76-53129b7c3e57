package com.gusto.match.model.entity.photo.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.gusto.match.model.entity.photo.dto.BgPhotoAlbumDiscountDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import java.math.BigDecimal;
import java.util.List;

/**
 * 创建/更新相册
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgPhotoAlbumUpsertReq {

    @Schema(description = "相册ID，更新时必填", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long albumId;

    @Schema(description = "关联的赛事ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long raceId;

    @Schema(description = "第三方相册ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> thirdAlbumIdList;

    @Schema(description = "摄影师比例，0.3表示30%", requiredMode = Schema.RequiredMode.REQUIRED)
    @PositiveOrZero
    private BigDecimal photographerRatio;

    @Schema(description = "公司比例，0.3表示30%", requiredMode = Schema.RequiredMode.REQUIRED)
    @PositiveOrZero
    private BigDecimal companyRatio;

    @Schema(description = "单张照片价格", requiredMode = Schema.RequiredMode.REQUIRED)
    @PositiveOrZero
    private BigDecimal photoAmount;

    @Schema(description = "折扣配置列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @Valid
    private List<BgPhotoAlbumDiscountDTO> discountList;

    @Schema(description = "备注信息")
    private String remark;

    @Schema(description = "状态：0-默认 1-启用 2-禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer state;

}
