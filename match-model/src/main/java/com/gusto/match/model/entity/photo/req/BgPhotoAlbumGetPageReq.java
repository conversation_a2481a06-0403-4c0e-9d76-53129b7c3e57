package com.gusto.match.model.entity.photo.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分页获取相册列表
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgPhotoAlbumGetPageReq {

    @Schema(description = "关联的赛事ID")
    private Long raceId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "相册状态：0-默认 1-启用 2-禁用")
    private Integer status;

    @Schema(description = "current")
    private Long current = 1L;

    @Schema(description = "size")
    private Long size = 20L;

}
