package com.gusto.match.model.entity.photo.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 相册
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
public class BgPhotoAlbumDTO {

    @Schema(description = "相册ID")
    @TableId(type = IdType.AUTO)
    private Long albumId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "关联的赛事ID")
    private Long raceId;

    @Schema(description = "赛事名称")
    private String raceName;

    @Schema(description = "第三方相册ID列表")
    private List<String> thirdAlbumIdList;

    @Schema(description = "摄影师比例，0.3表示30%")
    private BigDecimal photographerRatio;

    @Schema(description = "公司比例，0.3表示30%")
    private BigDecimal companyRatio;

    @Schema(description = "单张照片价格")
    private BigDecimal photoAmount;

    @Schema(description = "折扣配置列表")
    private List<BgPhotoAlbumDiscountDTO> discountList;

    @Schema(description = "备注信息")
    private String remark;

    @Schema(description = "状态：0-默认 1-启用 2-禁用")
    private Integer state;

    @Schema(description = "createTime")
    private Long createTime;

    @Schema(description = "updateTime")
    private Long updateTime;

}
