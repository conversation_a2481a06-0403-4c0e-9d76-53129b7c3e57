package com.gusto.match.model.entity.photo.req;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;

/**
 * 创建/更新相册
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BgPhotoAlbumUpdateStateReq {

    @Schema(description = "相册ID，更新时必填", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Long albumId;

    @Schema(description = "状态：0-默认 1-启用 2-禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @Positive
    private Integer state;

}
