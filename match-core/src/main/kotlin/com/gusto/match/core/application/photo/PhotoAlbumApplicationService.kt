package com.gusto.match.core.application.photo

import com.baomidou.mybatisplus.core.metadata.IPage
import com.gusto.match.core.service.offline.OfflineRaceService
import com.gusto.match.core.service.photo.PhotoAlbumService
import com.gusto.match.model.entity.photo.dto.BgPhotoAlbumDTO
import com.gusto.match.model.entity.photo.req.BgPhotoAlbumGetPageReq
import com.gusto.match.model.entity.photo.req.BgPhotoAlbumUpdateStateReq
import com.gusto.match.model.entity.photo.req.BgPhotoAlbumUpsertReq
import com.gusto.match.model.util.mapstruct.PhotoMapUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import javax.validation.Valid

@Service
class PhotoAlbumApplicationService {

    @Autowired
    lateinit var albumService: PhotoAlbumService

    @Autowired
    lateinit var mapUtils: PhotoMapUtils

    @Autowired
    lateinit var offlineRaceService: OfflineRaceService

    /**
     * 分页获取相册列表
     */
    fun getPage(req: BgPhotoAlbumGetPageReq): IPage<BgPhotoAlbumDTO> {
        TODO("Not yet implemented")
    }

    /**
     * 获取相册详情
     */
    fun getDetail(albumId: Long): BgPhotoAlbumDTO {
        TODO("Not yet implemented")
    }

    /**
     * 创建相册 TODO photo clean cache
     */
    fun create(req: BgPhotoAlbumUpsertReq): Long {
        TODO("Not yet implemented")
    }

    /**
     * 更新相册信息 TODO photo clean cache
     */
    fun update(req: BgPhotoAlbumUpsertReq) {
        TODO("Not yet implemented")
    }

    /**
     * 更新相册状态 TODO photo clean cache
     */
    fun updateState(req: @Valid BgPhotoAlbumUpdateStateReq): Any {
        TODO("Not yet implemented")
    }

}