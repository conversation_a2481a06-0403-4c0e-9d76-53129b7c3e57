package com.gusto.match.core.satoken;

/**
 * 线下赛 11000-12000
 */
public final class AdminPermissionOfflineCode {

    // 线下赛号码
    public static final String OFFLINE_BIBNO_QUERY_USER = "OFFLINE_BIBNO_QUERY_USER";    // 11000. 分页获取用户号码列表
    public static final String OFFLINE_BIBNO_UPDATE_BATCH_IMPORT = "OFFLINE_BIBNO_UPDATE_BATCH_IMPORT";    // 11001. 批量导入用户号码
    public static final String OFFLINE_BIBNO_UPDATE_MANUAL = "OFFLINE_BIBNO_UPDATE_MANUAL";    // 11002. 手动更新用户号码
    public static final String OFFLINE_BIBNO_UPDATE_AUTO = "OFFLINE_BIBNO_UPDATE_AUTO";    // 11003. 自动分配用户号码
    public static final String OFFLINE_BIBNO_RULE_LIST = "OFFLINE_BIBNO_RULE_LIST";    // 11004. 获取号码规则列表
    public static final String OFFLINE_BIBNO_RULE_CREATE = "OFFLINE_BIBNO_RULE_CREATE";    // 11005. 创建号码规则
    public static final String OFFLINE_BIBNO_RULE_UPDATE = "OFFLINE_BIBNO_RULE_UPDATE";    // 11006. 更新号码规则
    public static final String OFFLINE_BIBNO_GENERATE = "OFFLINE_BIBNO_GENERATE";    // 11007. 生成号码
    public static final String OFFLINE_BIBNO_QUERY_GENERATE = "OFFLINE_BIBNO_QUERY_GENERATE";    // 11008. 分页获取生成号码列表
    public static final String OFFLINE_BIBNO_DELETE_GENERATE = "OFFLINE_BIBNO_DELETE_GENERATE";    // 11009. 删除生成号码
    public static final String OFFLINE_BIBNO_BATCH_ASSIGN = "OFFLINE_BIBNO_BATCH_ASSIGN";    // 11010. 批量分配号码
    public static final String OFFLINE_BIBNO_QUERY_RESERVED = "OFFLINE_BIBNO_QUERY_RESERVED";    // 11011. 分页获取预留号码列表
    public static final String OFFLINE_BIBNO_CREATE_RESERVED = "OFFLINE_BIBNO_CREATE_RESERVED";    // 11012. 创建预留号码
    public static final String OFFLINE_BIBNO_UPDATE_RESERVED = "OFFLINE_BIBNO_UPDATE_RESERVED";    // 11013. 更新预留号码
    public static final String OFFLINE_BIBNO_DELETE_RESERVED = "OFFLINE_BIBNO_DELETE_RESERVED";    // 11014. 删除预留号码
    public static final String OFFLINE_BIBNO_IMPORT_JOB_LIST = "OFFLINE_BIBNO_IMPORT_JOB_LIST";    // 11015. 获取批量导入用户号码任务列表
    public static final String OFFLINE_BIBNO_GENERATE_JOB_LIST = "OFFLINE_BIBNO_GENERATE_JOB_LIST";    // 11016. 获取生成号码任务列表
    public static final String OFFLINE_BIBNO_ASSIGN_JOB_LIST = "OFFLINE_BIBNO_ASSIGN_JOB_LIST";    // 11017. 获取分配号码任务列表
    public static final String OFFLINE_BATCH_UPDATE_STAR = "OFFLINE_BATCH_UPDATE_STAR";    // 11018. 批量设置号码星级
    public static final String OFFLINE_BATCH_UPDATE_STAR_JOB_LIST = "OFFLINE_BATCH_UPDATE_STAR_JOB_LIST";    // 11019. 获取批量设置号码星级任务列表

    // 线下赛通道
    public static final String OFFLINE_CHANNEL_LIST = "OFFLINE_CHANNEL_LIST";    // 11100. 获取通道列表
    public static final String OFFLINE_CHANNEL_CREATE = "OFFLINE_CHANNEL_CREATE";    // 11101. 创建通道
    public static final String OFFLINE_CHANNEL_UPDATE = "OFFLINE_CHANNEL_UPDATE";    // 11102. 更新通道
    public static final String OFFLINE_CHANNEL_DETAIL = "OFFLINE_CHANNEL_DETAIL";    // 11103. 获取通道详情
    public static final String OFFLINE_CHANNEL_UPDATE_SORT = "OFFLINE_CHANNEL_UPDATE_SORT";    // 11104. 更新通道排序
    public static final String OFFLINE_CHANNEL_DELETE = "OFFLINE_CHANNEL_DELETE";    // 11105. 删除通道

    // 线下赛文档
    public static final String OFFLINE_DOCUMENT_LIST = "OFFLINE_DOCUMENT_LIST";    // 11200. 获取文档列表
    public static final String OFFLINE_DOCUMENT_CREATE = "OFFLINE_DOCUMENT_CREATE";    // 11201. 创建文档
    public static final String OFFLINE_DOCUMENT_UPDATE = "OFFLINE_DOCUMENT_UPDATE";    // 11202. 更新文档
    public static final String OFFLINE_DOCUMENT_DETAIL = "OFFLINE_DOCUMENT_DETAIL";    // 11203. 获取文档详情
    public static final String OFFLINE_DOCUMENT_UPDATE_SORT = "OFFLINE_DOCUMENT_UPDATE_SORT";    // 11204. 更新文档排序
    public static final String OFFLINE_DOCUMENT_DELETE = "OFFLINE_DOCUMENT_DELETE";    // 11205. 删除文档

    // 线下赛文档
    public static final String OFFLINE_INVITE_CODE_QUERY = "OFFLINE_INVITE_CODE_QUERY";    // 11300. 分页获取邀请码列表
    public static final String OFFLINE_INVITE_CODE_CREATE_BATCH = "OFFLINE_INVITE_CODE_CREATE_BATCH";    // 11301. 批量创建邀请码
    public static final String OFFLINE_INVITE_CODE_CREATE_JOB_LIST = "OFFLINE_INVITE_CODE_CREATE_JOB_LIST";    // 11302. 获取创建邀请码任务列表
    public static final String OFFLINE_INVITE_CODE_QUERY_GLOBAL = "OFFLINE_INVITE_CODE_QUERY_GLOBAL";    // 11303. 分页获取全局邀请码列表
    public static final String OFFLINE_INVITE_CODE_CREATE_BATCH_GLOBAL = "OFFLINE_INVITE_CODE_CREATE_BATCH_GLOBAL";    // 11304. 批量创建全局邀请码

    // 线下赛项目
    public static final String OFFLINE_PROJECT_LIST = "OFFLINE_PROJECT_LIST";    // 11400. 获取项目列表
    public static final String OFFLINE_PROJECT_CREATE = "OFFLINE_PROJECT_CREATE";    // 11401. 创建项目
    public static final String OFFLINE_PROJECT_UPDATE = "OFFLINE_PROJECT_UPDATE";    // 11402. 更新项目
    public static final String OFFLINE_PROJECT_DETAIL = "OFFLINE_PROJECT_DETAIL";    // 11403. 获取项目详情
    public static final String OFFLINE_PROJECT_UPDATE_SORT = "OFFLINE_PROJECT_UPDATE_SORT";    // 11404. 更新项目排序
    public static final String OFFLINE_PROJECT_DELETE = "OFFLINE_PROJECT_DELETE";    // 11405. 删除项目

    // 线下赛退赛
    public static final String OFFLINE_QUIT_QUERY = "OFFLINE_QUIT_QUERY";    // 11500. 分页获取退赛记录列表
    public static final String OFFLINE_QUIT_UPDATE_STATE = "OFFLINE_QUIT_UPDATE_STATE";    // 11501. 手动退款
    public static final String OFFLINE_QUIT_UPDATE_STATE_BATCH = "OFFLINE_QUIT_UPDATE_STATE_BATCH";    // 11502. 批量退款
    public static final String OFFLINE_REFUND_DETAIL_PAGE = "OFFLINE_REFUND_DETAIL_PAGE";    // 11503. 分页获取退款明细列表

    // 线下赛
    public static final String OFFLINE_RACE_QUERY = "OFFLINE_RACE_QUERY";    // 11600. 分页获取线下赛列表
    public static final String OFFLINE_RACE_CREATE = "OFFLINE_RACE_CREATE";    // 11601. 创建线下赛
    public static final String OFFLINE_RACE_UPDATE = "OFFLINE_RACE_UPDATE";    // 11602. 更新线下赛
    public static final String OFFLINE_RACE_DETAIL = "OFFLINE_RACE_DETAIL";    // 11603. 获取线下赛详情
    public static final String OFFLINE_RACE_UPDATE_SORT = "OFFLINE_RACE_UPDATE_SORT";    // 11604. 更新线下赛排序
    public static final String OFFLINE_RACE_LIST_DOMAIN = "OFFLINE_RACE_LIST_DOMAIN";    // 11605. 获取线下赛域名列表
    public static final String OFFLINE_RACE_QUIT_SETTING = "OFFLINE_RACE_QUIT_SETTING";    // 11606. 获取线下赛退赛设置
    public static final String OFFLINE_RACE_QUIT_SETTING_UPDATE = "OFFLINE_RACE_QUIT_SETTING_UPDATE";    // 11607. 更新线下赛退赛设置
    public static final String OFFLINE_RACE_BALLOT_SETTING = "OFFLINE_RACE_BALLOT_SETTING";    // 11608. 获取线下赛抽签公示设置
    public static final String OFFLINE_RACE_BALLOT_SETTING_UPDATE = "OFFLINE_RACE_BALLOT_SETTING_UPDATE";    // 11609. 更新线下赛抽签公示设置
    public static final String OFFLINE_RACE_SELECT_NUMBER_SETTING = "OFFLINE_RACE_SELECT_NUMBER_SETTING";    // 11610. 获取线下赛选号设置
    public static final String OFFLINE_RACE_SELECT_NUMBER_SETTING_UPDATE = "OFFLINE_RACE_SELECT_NUMBER_SETTING_UPDATE";    // 11611. 更新线下赛选号设置
    public static final String OFFLINE_RACE_FOLLOW_SETTING = "OFFLINE_RACE_FOLLOW_SETTING";    // 11612. 获取线下赛关注设置
    public static final String OFFLINE_RACE_FOLLOW_SETTING_UPDATE = "OFFLINE_RACE_FOLLOW_SETTING_UPDATE";    // 11613. 更新线下赛关注设置
    public static final String OFFLINE_RACE_SMS_SETTING = "OFFLINE_RACE_SMS_SETTING";    // 11614. 获取线下赛短信设置
    public static final String OFFLINE_RACE_SMS_SETTING_UPDATE = "OFFLINE_RACE_SMS_SETTING_UPDATE";    // 11615. 更新线下赛短信设置
    public static final String OFFLINE_RACE_SIGN_DETAIL_BANNER = "OFFLINE_RACE_SIGN_DETAIL_BANNER";    // 11616. 获取线下赛报名详情广告
    public static final String OFFLINE_RACE_SIGN_DETAIL_BANNER_UPDATE = "OFFLINE_RACE_SIGN_DETAIL_BANNER_UPDATE";    // 11617. 更新线下赛报名详情广告

    // 线下赛模板
    public static final String OFFLINE_TEMPLATE_PROJECT_LIST = "OFFLINE_TEMPLATE_PROJECT_LIST";    // 11700. 获取项目模板设置列表
    public static final String OFFLINE_TEMPLATE_PROJECT_UPDATE = "OFFLINE_TEMPLATE_PROJECT_UPDATE";    // 11701. 更新项目证书模板设置
    public static final String OFFLINE_TEMPLATE_LIST = "OFFLINE_TEMPLATE_LIST";    // 11702. 获取模板列表
    public static final String OFFLINE_TEMPLATE_CREATE = "OFFLINE_TEMPLATE_CREATE";    // 11703. 创建模板
    public static final String OFFLINE_TEMPLATE_UPDATE = "OFFLINE_TEMPLATE_UPDATE";    // 11704. 更新模板
    public static final String OFFLINE_TEMPLATE_DETAIL = "OFFLINE_TEMPLATE_DETAIL";    // 11705. 获取模板

    // 线下赛用户成绩
    public static final String OFFLINE_USER_RESULT_QUERY = "OFFLINE_USER_RESULT_QUERY";    // 11800. 分页获取成绩列表
    public static final String OFFLINE_USER_RESULT_UPDATE = "OFFLINE_USER_RESULT_UPDATE";    // 11801. 更新成绩
    public static final String OFFLINE_USER_RESULT_DETAIL = "OFFLINE_USER_RESULT_DETAIL";    // 11802. 获取成绩详情
    public static final String OFFLINE_USER_RESULT_IMPORT_BATCH = "OFFLINE_USER_RESULT_IMPORT_BATCH";    // 11803. 批量导入成绩
    public static final String OFFLINE_USER_RESULT_IMPORT_JOB_LIST = "OFFLINE_USER_RESULT_IMPORT_JOB_LIST";    // 11804. 获取导入成绩任务列表

    // 线下赛用户报名
    public static final String OFFLINE_USER_SIGN_QUERY = "OFFLINE_USER_SIGN_QUERY";    // 11900. 分页获取用户报名列表
    public static final String OFFLINE_USER_SIGN_UPDATE = "OFFLINE_USER_SIGN_UPDATE";    // 11901. 更新用户报名
    public static final String OFFLINE_USER_SIGN_DETAIL = "OFFLINE_USER_SIGN_DETAIL";    // 11902. 获取用户报名详情
    public static final String OFFLINE_USER_SIGN_QUIT = "OFFLINE_USER_SIGN_QUIT";    // 11903. 退赛
    public static final String OFFLINE_USER_SIGN_UPDATE_BALLOT = "OFFLINE_USER_SIGN_UPDATE_BALLOT";    // 11904. 更新中签状态
    public static final String OFFLINE_USER_SIGN_BATCH_UPDATE_BALLOT = "OFFLINE_USER_SIGN_BATCH_UPDATE_BALLOT";    // 11905. 批量更新中签状态
    public static final String OFFLINE_USER_SIGN_BATCH_IMPORT = "OFFLINE_USER_SIGN_BATCH_IMPORT";    // 11906. 导入报名名单
    public static final String OFFLINE_USER_SIGN_IMPORT_JOB_LIST = "OFFLINE_USER_SIGN_IMPORT_JOB_LIST";    // 11907. 获取导入报名名单任务列表
    public static final String OFFLINE_USER_SIGN_BATCH_IMPORT_BALLOT = "OFFLINE_USER_SIGN_BATCH_IMPORT_BALLOT";    // 11908. 导入中签名单
    public static final String OFFLINE_USER_SIGN_IMPORT_BALLOT_JOB_LIST = "OFFLINE_USER_SIGN_IMPORT_BALLOT_JOB_LIST";    // 11909. 获取导入中签名单任务列表
    public static final String OFFLINE_USER_SIGN_QUIT_REFUND = "OFFLINE_USER_SIGN_QUIT_REFUND";    // 11910. 退款

    // 线下赛用户报名审核
    public static final String OFFLINE_USER_SIGN_REVIEW_QUERY = "OFFLINE_USER_SIGN_REVIEW_QUERY";    // 12000. 分页获取审核列表
    public static final String OFFLINE_USER_SIGN_REVIEW_UPDATE_STATE = "OFFLINE_USER_SIGN_REVIEW_UPDATE_STATE";    // 12001. 更新审核状态
    public static final String OFFLINE_USER_SIGN_CHECK_QUALIFICATION = "OFFLINE_USER_SIGN_CHECK_QUALIFICATION";    // 12002. 检查资格赛是否完赛

    // 线下赛黑名单记录
    public static final String OFFLINE_BLACKLIST_RECORD_LIST = "OFFLINE_BLACKLIST_RECORD_LIST";    // 12100. 获取黑名单记录列表
    public static final String OFFLINE_BLACKLIST_RECORD_CREATE = "OFFLINE_BLACKLIST_RECORD_CREATE";    // 12101. 创建黑名单记录
    public static final String OFFLINE_BLACKLIST_RECORD_UPDATE = "OFFLINE_BLACKLIST_RECORD_UPDATE";    // 12102. 更新黑名单记录
    public static final String OFFLINE_BLACKLIST_RECORD_DETAIL = "OFFLINE_BLACKLIST_RECORD_DETAIL";    // 12103. 获取黑名单记录详情
    public static final String OFFLINE_BLACKLIST_RECORD_DELETE = "OFFLINE_BLACKLIST_RECORD_DELETE";    // 12104. 删除黑名单记录

    // 线下赛发布
    public static final String OFFLINE_RACE_QUERY_TENANT = "OFFLINE_RACE_QUERY_TENANT";    // 12200. 分页获取其他租户线下赛列表
    public static final String OFFLINE_RACE_UPDATE_APP_PUBLISH_STATE = "OFFLINE_RACE_UPDATE_APP_PUBLISH_STATE";    // 12201. 更新线下赛APP发布状态
    public static final String OFFLINE_RACE_UPDATE_APP_PUBLISH_STATE_BY_TENANT = "OFFLINE_RACE_UPDATE_APP_PUBLISH_STATE_BY_TENANT";    // 12202. 其他租户更新线下赛APP发布状态
    public static final String OFFLINE_RACE_UPDATE_SETTLE_STATE = "OFFLINE_RACE_UPDATE_SETTLE_STATE";    // 12203. 更新线下赛结算状态

    // 线下赛文档
    public static final String OFFLINE_ROUTE_LIST = "OFFLINE_ROUTE_LIST";    // 12300. 获取路线列表
    public static final String OFFLINE_ROUTE_CREATE = "OFFLINE_ROUTE_CREATE";    // 12301. 创建路线
    public static final String OFFLINE_ROUTE_UPDATE = "OFFLINE_ROUTE_UPDATE";    // 12302. 更新路线
    public static final String OFFLINE_ROUTE_DETAIL = "OFFLINE_ROUTE_DETAIL";    // 12303. 获取路线详情
    public static final String OFFLINE_ROUTE_UPDATE_SORT = "OFFLINE_ROUTE_UPDATE_SORT";    // 12304. 更新路线排序
    public static final String OFFLINE_ROUTE_DELETE = "OFFLINE_ROUTE_DELETE";    // 12305. 删除路线

    // 线下赛计时
    public static final String OFFLINE_TIMING_CREATE = "OFFLINE_TIMING_CREATE";    // 12400. 创建计时
    public static final String OFFLINE_TIMING_UPDATE = "OFFLINE_TIMING_UPDATE";    // 12401. 更新计时
    public static final String OFFLINE_TIMING_DETAIL = "OFFLINE_TIMING_DETAIL";    // 12402. 获取计时详情

    // 线下赛标签
    public static final String OFFLINE_TAG_QUERY = "OFFLINE_TAG_QUERY";    // 12500. 分页获取线下赛标签列表
    public static final String OFFLINE_TAG_CREATE = "OFFLINE_TAG_CREATE";    // 12501. 创建线下赛标签
    public static final String OFFLINE_TAG_DELETE = "OFFLINE_TAG_DELETE";    // 12502. 删除线下赛标签

    // 其他证书
    public static final String OTHER_CERT_QUERY = "OTHER_CERT_QUERY";    // 12600. 查询其他证书
    public static final String OTHER_CERT_DETAIL = "OTHER_CERT_DETAIL";    // 12601. 获取其他证书详情
    public static final String OTHER_CERT_UPDATE = "OTHER_CERT_UPDATE";    // 12602. 更新其他证书
    public static final String OTHER_CERT_DELETE = "OTHER_CERT_DELETE";    // 12603. 删除其他证书

    // 线下赛统计
    public static final String OFFLINE_STAT_SIGN_SOURCE = "OFFLINE_STAT_SIGN_SOURCE";    // 12600. 获取报名来源统计

    // 相册管理
    public static final String PHOTO_ALBUM_QUERY = "PHOTO_ALBUM_QUERY";    // 12700. 分页获取相册列表
    public static final String PHOTO_ALBUM_DETAIL = "PHOTO_ALBUM_DETAIL";    // 12701. 获取相册详情
    public static final String PHOTO_ALBUM_CREATE = "PHOTO_ALBUM_CREATE";    // 12702. 创建相册
    public static final String PHOTO_ALBUM_UPDATE = "PHOTO_ALBUM_UPDATE";    // 12703. 更新相册

}
