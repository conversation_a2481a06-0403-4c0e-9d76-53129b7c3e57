package com.gusto.match.model.mybatis;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gusto.match.model.entity.photo.PhotoAlbumDiscount;

import java.io.IOException;
import java.util.List;

/**
 * 相册折扣配置列表JSON类型处理器
 * 
 * <AUTHOR>
 * @since 2025-08-25
 */
public class ListPhotoAlbumDiscountJsonTypeHandler extends JacksonTypeHandler {
    private static final TypeReference<List<PhotoAlbumDiscount>> typeReference = new TypeReference<>() {
    };

    public ListPhotoAlbumDiscountJsonTypeHandler(Class<?> type) {
        super(type);
    }

    @Override
    protected Object parse(String json) {
        try {
            return getObjectMapper().readValue(json, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
