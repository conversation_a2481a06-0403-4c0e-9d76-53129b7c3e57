package com.gusto.match.model.entity.photo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 相册折扣配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Data
public class BgPhotoAlbumDiscountDTO implements Serializable {

    private static final long serialVersionUID = -3156891294214347269L;

    @Schema(description = "购买数量")
    @Positive
    private Integer quantity;

    @Schema(description = "折扣比例，0.3表示30%")
    @Positive
    private BigDecimal discountRatio;

}
