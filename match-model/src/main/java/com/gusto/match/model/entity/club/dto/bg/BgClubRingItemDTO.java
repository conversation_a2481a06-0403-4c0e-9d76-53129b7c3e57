package com.gusto.match.model.entity.club.dto.bg;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 后台-跑团擂台
 *
 * <AUTHOR>
 * @since 2022-10-11
 */
@Data
public class BgClubRingItemDTO {

    @Schema(description = "擂台ID")
    private Long ringId;

    @Schema(description = "活动名称")
    private String name;

    @Schema(description = "开始时间")
    private Long startTime;

    @Schema(description = "结束时间")
    private Long endTime;

    @Schema(description = "PK类型：0-默认 1-跑团PK 2-区域PK")
    private Integer pkType;

    @Schema(description = "是否置顶")
    private Boolean top;

    @Schema(description = "是否发布")
    private Boolean publish;

    @Schema(description = "创建时间")
    private Long createTime;

    @Schema(description = "状态：0-禁用 1-未开始 2-进行中 3-已结束")
    private Integer state;

    @Schema(description = "排序，倒序")
    private Integer sort;

}
