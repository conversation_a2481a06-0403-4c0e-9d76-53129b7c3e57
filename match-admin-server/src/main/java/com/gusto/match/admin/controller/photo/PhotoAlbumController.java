package com.gusto.match.admin.controller.photo;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.gusto.framework.core.bean.CommonResult;
import com.gusto.match.core.application.photo.PhotoAlbumApplicationService;
import com.gusto.match.core.operatelog.OperateLog;
import com.gusto.match.core.satoken.AdminPermissionOfflineCode;
import com.gusto.match.core.satoken.StpAdminUtil;
import com.gusto.match.core.swagger.ApiVersion;
import com.gusto.match.core.swagger.CustomVersion;
import com.gusto.match.core.tenant.TenantEnable;
import com.gusto.match.model.entity.photo.dto.BgPhotoAlbumDTO;
import com.gusto.match.model.entity.photo.req.BgPhotoAlbumGetPageReq;
import com.gusto.match.model.entity.photo.req.BgPhotoAlbumUpdateStateReq;
import com.gusto.match.model.entity.photo.req.BgPhotoAlbumUpsertReq;
import com.gusto.match.model.enums.admin.OperateTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 相册管理 控制类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-25
 */
@Tag(name = "相册管理")
@SaCheckLogin(type = StpAdminUtil.TYPE)
@Validated
@RestController
@RequestMapping("photo/album")
public class PhotoAlbumController {

    @Autowired
    private PhotoAlbumApplicationService service;

    /**
     * 分页获取相册列表
     */
    @ApiVersion(CustomVersion.OFFLINE)
    @Operation(summary = "分页获取相册列表")
    @SaCheckPermission(value = AdminPermissionOfflineCode.PHOTO_ALBUM_QUERY, type = StpAdminUtil.TYPE)
    @GetMapping("page")
    public CommonResult<IPage<BgPhotoAlbumDTO>> getPage(BgPhotoAlbumGetPageReq req) {
        return CommonResult.success(service.getPage(req));
    }

    /**
     * 获取相册详情
     */
    @ApiVersion(CustomVersion.OFFLINE)
    @Operation(summary = "获取相册详情")
    @Parameter(name = "albumId", description = "相册ID", required = true)
    @SaCheckPermission(value = AdminPermissionOfflineCode.PHOTO_ALBUM_DETAIL, type = StpAdminUtil.TYPE)
    @TenantEnable
    @GetMapping("detail")
    public CommonResult<BgPhotoAlbumDTO> getDetail(
        @RequestParam("albumId") Long albumId
    ) {
        return CommonResult.success(service.getDetail(albumId));
    }

    /**
     * 创建相册
     */
    @ApiVersion(CustomVersion.OFFLINE)
    @Operation(summary = "创建相册")
    @SaCheckPermission(value = AdminPermissionOfflineCode.PHOTO_ALBUM_CREATE, type = StpAdminUtil.TYPE)
    @OperateLog(module = "线下赛", type = OperateTypeEnum.CREATE)
    @PostMapping("create")
    public CommonResult<Long> create(@Valid @RequestBody BgPhotoAlbumUpsertReq req) {
        return CommonResult.success(service.create(req));
    }

    /**
     * 更新相册
     */
    @ApiVersion({CustomVersion.OFFLINE, CustomVersion.OFFLINE_TENANT})
    @Operation(summary = "更新相册")
    @SaCheckPermission(value = AdminPermissionOfflineCode.PHOTO_ALBUM_UPDATE, type = StpAdminUtil.TYPE)
    @OperateLog(module = "线下赛", type = OperateTypeEnum.UPDATE)
    @PostMapping("update")
    public void update(@Valid @RequestBody BgPhotoAlbumUpsertReq req) {
        service.update(req);
    }

    /**
     * 更新相册状态
     */
    @ApiVersion({CustomVersion.OFFLINE, CustomVersion.OFFLINE_TENANT})
    @Operation(summary = "更新相册状态")
    @SaCheckPermission(value = AdminPermissionOfflineCode.PHOTO_ALBUM_UPDATE, type = StpAdminUtil.TYPE)
    @OperateLog(module = "线下赛", type = OperateTypeEnum.UPDATE)
    @PostMapping("update/state")
    public void updateState(@Valid @RequestBody BgPhotoAlbumUpdateStateReq req) {
        service.updateState(req);
    }

}
